<?php

namespace Tests\Nzoom\Export\Streamer;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Streamer\GeneratorFileStreamer;
use Nzoom\Export\Streamer\StreamHeaders;

/**
 * Test case for GeneratorFileStreamer
 *
 * Tests generator-based file streaming functionality
 */
class GeneratorFileStreamerTest extends ExportTestCase
{
    private GeneratorFileStreamer $streamer;

    protected function setUp(): void
    {
        parent::setUp();
    }

    // Test constructor and basic functionality

    public function testConstructorWithBasicGenerator(): void
    {
        $generatorFunction = function() {
            yield 'chunk1';
            yield 'chunk2';
            yield 'chunk3';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        $this->assertEquals('test.txt', $this->streamer->getFilename());
        $this->assertEquals('text/plain', $this->streamer->getMimeType());
        $this->assertNull($this->streamer->getTotalSize());
    }

    public function testConstructorWithTotalSize(): void
    {
        $generatorFunction = function() {
            yield 'test data';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain', 1024);

        $this->assertEquals(1024, $this->streamer->getTotalSize());
        
        // Should set Content-Length header
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('1024', $headers->getHeader('Content-Length'));
    }

    public function testConstructorWithDefaultMimeType(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.bin');

        $this->assertEquals('application/octet-stream', $this->streamer->getMimeType());
    }

    public function testConstructorWithInvalidGeneratorFunction(): void
    {
        $invalidFunction = function() {
            return 'not a generator'; // Returns string instead of generator
        };

        $this->expectException(\TypeError::class);

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    // Test total size management

    public function testSetTotalSize(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt');
        
        $result = $this->streamer->setTotalSize(2048);
        
        $this->assertSame($this->streamer, $result); // Should return self for fluent interface
        $this->assertEquals(2048, $this->streamer->getTotalSize());
        
        // Should update Content-Length header
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('2048', $headers->getHeader('Content-Length'));
    }

    public function testSetTotalSizeToNull(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain', 1024);
        
        $this->streamer->setTotalSize(null);
        
        $this->assertNull($this->streamer->getTotalSize());
    }

    // Test streaming functionality

    public function testPerformStreamingDirectly(): void
    {
        $chunks = [];
        $generatorFunction = function() use (&$chunks) {
            $data = ['Hello ', 'World', '!'];
            foreach ($data as $chunk) {
                $chunks[] = $chunk; // Track what was yielded
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Use reflection to test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        // Call performStreaming directly
        $method->invoke($this->streamer);

        // Verify that all chunks were processed
        $this->assertEquals(['Hello ', 'World', '!'], $chunks);
    }

    public function testStreamWithSimpleGenerator(): void
    {
        $outputChunks = [];
        $generatorFunction = function() use (&$outputChunks) {
            $chunks = ['Hello ', 'World', '!'];
            foreach ($chunks as $chunk) {
                $outputChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test performStreaming directly to verify output logic
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify the expected content was output
        $this->assertEquals(['Hello ', 'World', '!'], $outputChunks);
        $this->assertEquals('Hello World!', $output);
    }

    public function testStreamWithEmptyGenerator(): void
    {
        $generatorFunction = function() {
            return;
            yield; // This will never be reached
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'empty.txt', 'text/plain');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Empty generator should produce no output
        $this->assertEquals('', $output);
    }

    public function testStreamWithNullChunks(): void
    {
        $processedChunks = [];
        $generatorFunction = function() use (&$processedChunks) {
            $chunks = ['start', null, 'middle', '', 'end'];
            foreach ($chunks as $chunk) {
                $processedChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify all chunks were processed (including null and empty)
        $this->assertEquals(['start', null, 'middle', '', 'end'], $processedChunks);
        // Only non-null, non-empty chunks should be output
        $this->assertEquals('startmiddleend', $output);
    }

    public function testStreamWithNonStringChunks(): void
    {
        $processedChunks = [];
        $generatorFunction = function() use (&$processedChunks) {
            $chunks = ['text', 123, 45.67, true];
            foreach ($chunks as $chunk) {
                $processedChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify all chunks were processed
        $this->assertEquals(['text', 123, 45.67, true], $processedChunks);
        // Non-string chunks should be converted to strings
        $this->assertEquals('text12345.671', $output);
    }

    public function testStreamWithLargeGenerator(): void
    {
        $chunkCount = 0;
        $generatorFunction = function() use (&$chunkCount) {
            for ($i = 1; $i <= 100; $i++) {
                $chunkCount++;
                yield "chunk{$i}\n";
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'large.txt', 'text/plain');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify all chunks were generated
        $this->assertEquals(100, $chunkCount);
        // Verify output contains expected content
        $this->assertStringContainsString('chunk1', $output);
        $this->assertStringContainsString('chunk100', $output);
        $this->assertEquals(100, substr_count($output, 'chunk'));
    }

    // Test reset functionality

    public function testReset(): void
    {
        $callCount = 0;
        $generatorFunction = function() use (&$callCount) {
            $callCount++;
            yield "call{$callCount}";
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test performStreaming directly for first call
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $firstOutput = ob_get_clean();

        // Reset and stream again
        $result = $this->streamer->reset();

        $this->assertSame($this->streamer, $result); // Should return self for fluent interface

        ob_start();
        $method->invoke($this->streamer);
        $secondOutput = ob_get_clean();

        // Should have called the generator function twice
        $this->assertEquals(2, $callCount);
        $this->assertEquals('call1', $firstOutput);
        $this->assertEquals('call2', $secondOutput);
    }

    // Test headers functionality

    public function testHeadersIntegration(): void
    {
        $generatorFunction = function() {
            yield 'test data';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');
        
        // Get headers instance
        $headers = $this->streamer->getHeaders();
        $this->assertInstanceOf(StreamHeaders::class, $headers);
        
        // Should have file content headers set during streaming
        ob_start();
        $this->streamer->stream();
        ob_get_clean();
        
        $this->assertEquals('text/plain', $headers->getHeader('Content-Type'));
        $this->assertStringContainsString('attachment; filename="test.txt"', $headers->getHeader('Content-Disposition'));
    }

    public function testCustomHeaders(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Set custom headers
        $customHeaders = new StreamHeaders();
        $customHeaders->addHeader('X-Custom-Header', 'custom-value');
        $this->streamer->setHeaders($customHeaders);

        $headers = $this->streamer->getHeaders();
        $this->assertEquals('custom-value', $headers->getHeader('X-Custom-Header'));
    }

    // Test cache functionality

    public function testCacheHeaders(): void
    {
        $generatorFunction = function() {
            yield 'cached content';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'cached.txt', 'text/plain');

        // Set cache parameters
        $this->streamer->setCacheExpires(3600);
        $this->streamer->setETag('test-etag');
        $this->streamer->setLastModified(time() - 1000);

        // Test that the cache methods work without error
        $headers = $this->streamer->getHeaders();
        $this->assertNotNull($headers);

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        $this->assertEquals('cached content', $output);
    }

    // Test time increment functionality

    public function testTimeIncrement(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Set time increment
        $this->streamer->setTimeIncrement(60);

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify streaming completed and produced expected output
        $this->assertEquals('test', $output);
    }

    // Test error handling

    public function testGeneratorWithException(): void
    {
        $generatorFunction = function() {
            yield 'start';
            throw new \RuntimeException('Generator error');
            yield 'never reached';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'error.txt', 'text/plain');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Generator error');

        ob_start();
        try {
            $this->streamer->stream();
        } finally {
            ob_end_clean();
        }
    }

    public function testGeneratorFunctionThrowsException(): void
    {
        $generatorFunction = function() {
            throw new \RuntimeException('Function error');
        };

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Function error');

        new GeneratorFileStreamer($generatorFunction, 'test.txt');
    }

    // Test generator function validation

    public function testGeneratorFunctionReturnsNull(): void
    {
        $invalidFunction = function() {
            return null;
        };

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Generator function must return a Generator instance');

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    public function testGeneratorFunctionReturnsArray(): void
    {
        $invalidFunction = function() {
            return ['not', 'a', 'generator'];
        };

        $this->expectException(\TypeError::class);

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    // Test edge cases

    public function testStreamWithVeryLongChunks(): void
    {
        $totalLength = 0;
        $generatorFunction = function() use (&$totalLength) {
            // Generate a very long chunk
            $chunk1 = str_repeat('A', 10000);
            $chunk2 = str_repeat('B', 5000);
            $totalLength = strlen($chunk1) + strlen($chunk2);
            yield $chunk1;
            yield $chunk2;
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'long.txt', 'text/plain');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify the expected total length was generated
        $this->assertEquals(15000, $totalLength);
        $this->assertEquals(15000, strlen($output));
        $this->assertStringStartsWith('AAAA', $output);
        $this->assertStringEndsWith('BBBB', $output);
    }

    public function testStreamWithBinaryData(): void
    {
        $binaryData = [];
        $generatorFunction = function() use (&$binaryData) {
            // Generate some binary data
            $chunk1 = pack('C*', 0, 1, 2, 3, 255);
            $chunk2 = pack('n', 65535); // Big-endian 16-bit
            $binaryData = [$chunk1, $chunk2];
            yield $chunk1;
            yield $chunk2;
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'binary.bin', 'application/octet-stream');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify binary data was generated correctly
        $this->assertCount(2, $binaryData);
        $this->assertEquals(5, strlen($binaryData[0])); // First chunk: 5 bytes
        $this->assertEquals(2, strlen($binaryData[1])); // Second chunk: 2 bytes
        $this->assertEquals(0, ord($binaryData[0][0])); // First byte is 0
        $this->assertEquals(255, ord($binaryData[0][4])); // Fifth byte is 255

        // Verify output contains the binary data
        $this->assertEquals(7, strlen($output)); // Total: 5 + 2 bytes
        $this->assertEquals($binaryData[0] . $binaryData[1], $output);
    }

    public function testStreamWithUnicodeData(): void
    {
        $unicodeChunks = [];
        $generatorFunction = function() use (&$unicodeChunks) {
            $chunks = ['Hello ', 'Wörld ', '世界', ' 🌍'];
            foreach ($chunks as $chunk) {
                $unicodeChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'unicode.txt', 'text/plain; charset=utf-8');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify unicode data was processed
        $this->assertCount(4, $unicodeChunks);
        $this->assertContains('Wörld ', $unicodeChunks);
        $this->assertContains('世界', $unicodeChunks);
        $this->assertContains(' 🌍', $unicodeChunks);

        // Verify output contains the unicode content
        $this->assertEquals('Hello Wörld 世界 🌍', $output);
    }

    // Test inheritance from FileStreamer

    public function testInheritsFromFileStreamer(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test inherited methods
        $this->assertEquals('test.txt', $this->streamer->getFilename());
        $this->assertEquals('text/plain', $this->streamer->getMimeType());

        // Test inherited functionality
        $this->streamer->setCacheExpires(1800);
        $this->streamer->setETag('inherited-etag');
        $this->streamer->setLastModified(time() - 500);

        // These should work without error
        $this->assertTrue(true);
    }

    // Test full streaming integration (without capturing output)

    public function testFullStreamingIntegration(): void
    {
        $processedChunks = [];
        $generatorFunction = function() use (&$processedChunks) {
            $chunks = ['chunk1', 'chunk2', 'chunk3'];
            foreach ($chunks as $chunk) {
                $processedChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'integration.txt', 'text/plain');

        // Test that full streaming completes without error
        $this->streamer->stream();

        // Verify that the generator was processed
        $this->assertEquals(['chunk1', 'chunk2', 'chunk3'], $processedChunks);
    }

    public function testFullStreamingWithReset(): void
    {
        $callCount = 0;
        $generatorFunction = function() use (&$callCount) {
            $callCount++;
            yield "call{$callCount}";
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'reset.txt', 'text/plain');

        // First full stream
        $this->streamer->stream();
        $firstCallCount = $callCount;

        // Reset and stream again
        $this->streamer->reset();
        $this->streamer->stream();

        // Should have called the generator function twice
        $this->assertEquals(1, $firstCallCount);
        $this->assertEquals(2, $callCount);
    }

    // Test multiple streaming calls

    public function testMultipleStreamCalls(): void
    {
        $callCount = 0;
        $generatorFunction = function() use (&$callCount) {
            $callCount++;
            yield "stream{$callCount}";
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'multi.txt', 'text/plain');

        // Test performStreaming directly for first call
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $firstOutput = ob_get_clean();
        $firstCallCount = $callCount;

        // Reset to allow multiple streams
        $this->streamer->reset();

        // Second stream (should create new generator)
        ob_start();
        $method->invoke($this->streamer);
        $secondOutput = ob_get_clean();

        // Should have called the generator function twice
        $this->assertEquals(1, $firstCallCount);
        $this->assertEquals(2, $callCount);
        $this->assertEquals('stream1', $firstOutput);
        $this->assertEquals('stream2', $secondOutput);
    }

    // Test with real-world scenarios

    public function testCsvDataGeneration(): void
    {
        $csvLines = [];
        $generatorFunction = function() use (&$csvLines) {
            $lines = ["ID,Name,Email\n"];
            for ($i = 1; $i <= 5; $i++) {
                $lines[] = "{$i},User{$i},user{$i}@example.com\n";
            }

            foreach ($lines as $line) {
                $csvLines[] = $line;
                yield $line;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'data.csv', 'text/csv');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify CSV data was generated correctly
        $this->assertCount(6, $csvLines); // Header + 5 data rows
        $this->assertEquals("ID,Name,Email\n", $csvLines[0]);
        $this->assertEquals("1,User1,<EMAIL>\n", $csvLines[1]);
        $this->assertEquals("5,User5,<EMAIL>\n", $csvLines[5]);

        // Verify output contains the CSV content
        $expectedOutput = implode('', $csvLines);
        $this->assertEquals($expectedOutput, $output);
    }

    public function testJsonDataGeneration(): void
    {
        $jsonParts = [];
        $generatorFunction = function() use (&$jsonParts) {
            $parts = ['{"data":['];
            for ($i = 1; $i <= 3; $i++) {
                if ($i > 1) $parts[] = ',';
                $parts[] = json_encode(['id' => $i, 'name' => "User{$i}"]);
            }
            $parts[] = ']}';

            foreach ($parts as $part) {
                $jsonParts[] = $part;
                yield $part;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'data.json', 'application/json');

        // Test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        ob_start();
        $method->invoke($this->streamer);
        $output = ob_get_clean();

        // Verify JSON data was generated correctly
        $this->assertNotEmpty($jsonParts);
        $this->assertEquals('{"data":[', $jsonParts[0]);
        $this->assertEquals(']}', end($jsonParts));

        // Reconstruct and validate JSON
        $fullJson = implode('', $jsonParts);
        $this->assertEquals($fullJson, $output);

        $data = json_decode($fullJson, true);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('data', $data);
        $this->assertCount(3, $data['data']);
        $this->assertEquals('User1', $data['data'][0]['name']);
        $this->assertEquals('User3', $data['data'][2]['name']);
    }

    // Test cleanup and resource management

    public function testCleanupAfterException(): void
    {
        $generatorFunction = function() {
            yield 'start';
            throw new \RuntimeException('Test exception');
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'error.txt', 'text/plain');

        // Store original output buffer level
        $originalLevel = ob_get_level();

        try {
            ob_start();
            $this->streamer->stream();
            $this->fail('Expected exception was not thrown');
        } catch (\RuntimeException $e) {
            ob_end_clean();
            // Exception was expected
        }

        // Output buffer level should be restored (this is handled by FileStreamer)
        $this->assertEquals($originalLevel, ob_get_level());
    }

    protected function tearDown(): void
    {
        // Clean up any remaining output buffers
        while (ob_get_level() > 0) {
            ob_end_clean();
        }

        parent::tearDown();
    }
}
